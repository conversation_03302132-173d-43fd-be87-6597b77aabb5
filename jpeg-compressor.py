#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# dependencies = [
#     "click",
#     "pillow",
#     "rich",
# ]
# ///

import os
import sys
import subprocess
import tempfile
import shlex
from pathlib import Path
import click
from PIL import Image
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn, TimeRemainingColumn
from rich.markup import escape
from rich.text import Text

Image.MAX_IMAGE_PIXELS = None

# Initialize Rich console for pretty output
console = Console()

# Minimum size in bytes (1 MB by default)
MIN_SIZE = 1024 * 1024  # 1 MB

# Magic bytes for different image formats
IMAGE_MAGIC_BYTES = {
    b'\xff\xd8\xff': 'jpeg',
    b'\x89PNG\r\n\x1a\n': 'png',
    b'GIF87a': 'gif',
    b'GIF89a': 'gif',
    b'BM': 'bmp',
    b'II*\x00': 'tiff',
    b'MM\x00*': 'tiff',
    b'RIFF': 'webp',  # Need to check for WEBP after RIFF
}

def detect_image_type(file_path):
    """Detect image type using magic bytes instead of file extension."""
    try:
        with open(file_path, 'rb') as f:
            # Read first 12 bytes to check magic bytes
            header = f.read(12)

            # Check for JPEG
            if header.startswith(b'\xff\xd8\xff'):
                return 'jpeg'

            # Check for PNG
            if header.startswith(b'\x89PNG\r\n\x1a\n'):
                return 'png'

            # Check for GIF
            if header.startswith(b'GIF87a') or header.startswith(b'GIF89a'):
                return 'gif'

            # Check for BMP
            if header.startswith(b'BM'):
                return 'bmp'

            # Check for TIFF
            if header.startswith(b'II*\x00') or header.startswith(b'MM\x00*'):
                return 'tiff'

            # Check for WebP (RIFF container with WEBP signature)
            if header.startswith(b'RIFF') and len(header) >= 12:
                if header[8:12] == b'WEBP':
                    return 'webp'

            return None
    except Exception:
        return None

def is_supported_image(file_path):
    """Check if file is a supported image format using magic bytes."""
    image_type = detect_image_type(file_path)
    return image_type in ['jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp']

def find_images_in_directory(directory, recursive=False):
    """Find all supported images in a directory using magic bytes detection."""
    images = []
    search_pattern = "**/*" if recursive else "*"

    for file_path in Path(directory).glob(search_pattern):
        if file_path.is_file() and is_supported_image(file_path):
            images.append(file_path)

    return images

def clear_screen():
    """Clear the terminal screen."""
    # Use the appropriate clear command based on the OS
    os.system('cls' if os.name == 'nt' else 'clear')

def compress_image(file_path, quality=70, overwrite=True, min_size=MIN_SIZE):
    """Compress an image using MozJPEG with quality 70."""
    try:
        file_path = Path(file_path)

        # Detect image type using magic bytes
        image_type = detect_image_type(file_path)
        if not image_type:
            console.print(f"[bold red]Unsupported file format:[/bold red] {file_path}")
            return False, (file_path, 0, 0, 0)

        # Determine output path (same as input by default)
        if overwrite:
            output_path = file_path
            # Create a temporary file for the output
            temp_output = Path(tempfile.mktemp(suffix='.jpg'))
        else:
            # If not overwriting, create a new file with _compressed suffix
            output_path = file_path.with_stem(f"{file_path.stem}_compressed")
            output_path = output_path.with_suffix('.jpg')

        # For JPEGs, decompress first with djpeg, then recompress with cjpeg
        if image_type == 'jpeg':
            # Use absolute path to avoid issues with filenames starting with dashes
            abs_file_path = file_path.resolve()
            if overwrite:
                subprocess.run(
                    ["sh", "-c", f"djpeg {shlex.quote(str(abs_file_path))} | cjpeg -quality {quality} -outfile {shlex.quote(str(temp_output))}"],
                    check=True,
                    stderr=subprocess.PIPE,
                    text=True
                )
            else:
                subprocess.run(
                    ["sh", "-c", f"djpeg {shlex.quote(str(abs_file_path))} | cjpeg -quality {quality} -outfile {shlex.quote(str(output_path))}"],
                    check=True,
                    stderr=subprocess.PIPE,
                    text=True
                )
        else:
            # For other formats, convert to JPEG using PIL -> PPM -> cjpeg
            with Image.open(file_path) as img:
                # Convert to RGB if needed
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                temp_ppm = Path(tempfile.mktemp(suffix='.ppm'))
                img.save(temp_ppm)

                # Use cjpeg to create optimized JPEG
                if overwrite:
                    subprocess.run(
                        ["sh", "-c", f"cjpeg -quality {quality} {shlex.quote(str(temp_ppm))} > {shlex.quote(str(temp_output))}"],
                        check=True,
                        capture_output=True,
                        text=True
                    )
                else:
                    subprocess.run(
                        ["sh", "-c", f"cjpeg -quality {quality} {shlex.quote(str(temp_ppm))} > {shlex.quote(str(output_path))}"],
                        check=True,
                        capture_output=True,
                        text=True
                    )

                # Remove temporary PPM file
                temp_ppm.unlink()
        
        # Move temp file to original location if overwriting
        if overwrite:
            # Calculate stats before replacing the file
            original_size = os.path.getsize(file_path)
            compressed_size = os.path.getsize(temp_output)
            
            # Replace original with compressed version
            temp_output.replace(output_path)
        else:
            original_size = os.path.getsize(file_path)
            compressed_size = os.path.getsize(output_path)
            
        # Calculate compression statistics
        reduction_percent = (1 - compressed_size / original_size) * 100
        
        # Return the original file_path for consistent display
        return True, (file_path, original_size, compressed_size, reduction_percent)
    except Exception as e:
        console.print(f"[bold red]Error processing[/bold red] {file_path}: {str(e)}")
        # Return original path even on error for potential logging elsewhere if needed
        return False, (file_path, 0, 0, 0) # Return dummy stats on error


def process_images(images, quality=70, no_overwrite=False, min_size=MIN_SIZE, base_dir_for_display=None):
    """Process a batch of images with the given settings."""
    
    # Default to CWD if no base directory is provided
    if base_dir_for_display is None:
        base_dir_for_display = Path.cwd()
        
    # Create progress display with nicer columns
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(bar_width=40),
        TaskProgressColumn(text_format="[progress.percentage]{task.percentage:>3.0f}% ({task.completed}/{task.total})"),
        TimeRemainingColumn(),
    ) as progress:
        task = progress.add_task("[green]Compressing images...", total=len(images))
        
        success_count = 0
        compression_results = []
        
        for image_path in images:
            try:
                # Handle Path objects or strings properly
                if isinstance(image_path, Path):
                    path = image_path
                else:
                    # Clean up path - remove any quotes or escape sequences for strings
                    clean_path = str(image_path).strip().strip('"\'')
                    path = Path(clean_path)
                
                if not path.exists():
                    console.print(f"[bold red]File not found:[/bold red] {image_path}")
                    continue

                # Skip non-image files using magic bytes detection
                if not is_supported_image(path):
                    continue

                # Skip files smaller than the minimum size
                if os.path.getsize(path) < min_size:
                    continue
                
                # Update progress bar with current file
                progress.update(task, description=f"[cyan]Compressing[/cyan] {path.name}")
                
                # Compress image and collect stats
                success, stats = compress_image(path, quality=quality, overwrite=not no_overwrite, min_size=min_size)
                if success:
                    success_count += 1
                    compression_results.append(stats)
                    
            except Exception as e:
                console.print(f"[bold red]Error:[/bold red] {str(e)}")
            
            progress.advance(task)
    
    # After completion, show a summary of the compression results
    if compression_results:
        console.print("\n[bold]Compression Results:[/bold]")
        
        # Calculate total savings
        total_original = sum(result[1] for result in compression_results)
        total_compressed = sum(result[2] for result in compression_results)
        overall_reduction = (1 - total_compressed / total_original) * 100 if total_original > 0 else 0
        
        for path, original, compressed, reduction in compression_results:
            # Determine display name: relative path or just filename
            try:
                # Check if the path is relative to the base display directory and not identical
                if path.is_relative_to(base_dir_for_display) and path.parent != base_dir_for_display:
                    display_name = str(path.relative_to(base_dir_for_display))
                else:
                    display_name = path.name
            except ValueError:
                 # Fallback if relative_to fails (e.g., different drives on Windows)
                 display_name = path.name 
                 
            # Escape the name for Rich markup
            escaped_name = escape(display_name)
            
            console.print(Text(escaped_name, style="none"), f": [yellow]{original/1024:.1f}[/yellow] KB → [green]{compressed/1024:.1f}[/green] KB ([cyan]{reduction:.1f}%[/cyan])")
        
        console.print(f"\n[bold]Total:[/bold] [yellow]{total_original/1024/1024:.2f}[/yellow] MB → [green]{total_compressed/1024/1024:.2f}[/green] MB ([cyan]{overall_reduction:.1f}%[/cyan] reduction)")
        console.print(f"[bold green]Complete![/bold green] {success_count} of {len(images)} files compressed.")
    
    return success_count > 0


@click.command(context_settings=dict(help_option_names=['-h', '--help']))
@click.option('--quality', '-q', type=int, default=70, help='JPEG quality (1-100, default: 70)')
@click.option('--no-overwrite', is_flag=True, help="Don't overwrite original files, create new ones with _compressed suffix")
@click.option('--no-loop', is_flag=True, help="Don't loop for multiple batches of images")
@click.option('--dir', '-d', type=click.Path(exists=True, file_okay=False), help='Process all supported images in this directory')
@click.option('--recursive', '-r', is_flag=True, help='Recursively process images in subdirectories when a directory is specified')
@click.option('--min-size', type=float, default=0.9, help='Minimum file size in MB for compression (default: 0.9)')
@click.argument('images', nargs=-1, type=click.Path(exists=True))
def cli(images, quality, no_overwrite, no_loop, dir, recursive, min_size):
    """MozJPEG Image Compressor - Compress your images with quality 70
    
    Drag and drop images onto this script to compress them.
    
    Example: ./jpeg-compressor.py image1.jpg image2.png
    """
    # Determine the base directory for display purposes
    base_dir_for_display = Path(dir) if dir else Path.cwd()
    
    # Convert min_size from MB to bytes
    min_size_bytes = int(min_size * 1024 * 1024)

    target_images = []
    
    # Process directory specified with --dir
    if dir:
        dir_path = Path(dir)
        target_images.extend(find_images_in_directory(dir_path, recursive))
    
    # Process images provided as arguments
    if images:
        for img_arg in images:
            path = Path(img_arg)
            if path.is_dir():
                target_images.extend(find_images_in_directory(path, recursive))
            elif path.is_file():
                # Only add files that are supported image formats using magic bytes
                if is_supported_image(path):
                    target_images.append(path)

    # Remove duplicates that might arise from specifying both --dir and args
    target_images = sorted(list(set(target_images)))

    if target_images:
        clear_screen()
        # Pass base_dir_for_display to process_images
        if process_images(target_images, quality, no_overwrite, min_size_bytes, base_dir_for_display=base_dir_for_display):
            console.print("Processing complete for the provided images.")
        else:
            console.print("No images were compressed in this batch.")
        
        # Exit after processing if --dir or image arguments were provided directly
        # or if --no-loop is set.
        if not no_loop and (dir or images):
             # If dir or images were provided, run once and exit unless explicitly allowed
             # This logic might need refinement based on desired behavior
             # Currently: if dir or images provided, run once and exit unless --no-loop is FALSE (which is default)
             # Let's change this: if dir or images are provided, run once and exit.
             # Loop only if NO dir/images were provided initially.
            sys.exit(0)
        elif no_loop:
            sys.exit(0)
            
    # Interactive loop if no images/dir were provided initially and no-loop is not set
    continue_processing = not no_loop and not (dir or images)
    
    while continue_processing:
        console.print("[bold]MozJPEG Image Compressor[/bold]")
        console.print(f"Minimum file size: {min_size:.2f} MB")
        console.print("Drag and drop images or a folder onto this terminal window and press Enter:")
        console.print("(or type 'q' to quit)")
        
        # Read paths from input
        user_input = input().strip()
        
        # Check for exit command
        if user_input.lower() in ('q', 'quit', 'exit'):
            break
        
        # Clear screen to keep the interface clean
        clear_screen()
        
        # Parse the input to get a list of paths
        paths = []
        if user_input:
            try:
                # Try using shlex which properly handles quoted paths and escapes
                paths = shlex.split(user_input)
            except Exception:
                # Fallback: handle more complex inputs
                in_quote = False
                quote_char = None
                current_path = ''
                for char in user_input + ' ':  # Add space to ensure the last token is processed
                    if char in ['"', "'"]:
                        if not in_quote:
                            in_quote = True
                            quote_char = char
                        elif char == quote_char:
                            in_quote = False
                        current_path += char
                    elif char.isspace() and not in_quote:
                        if current_path:
                            paths.append(current_path)
                            current_path = ''
                    else:
                        current_path += char
        
        if not paths:
            console.print("[bold red]No input specified.[/bold red]\n")
            if no_loop:
                break
            continue

        # Analyze what was dropped - files or directories
        image_files = []
        directories = []
        
        for item in paths:
            # Handle PosixPath objects or strings correctly
            if isinstance(item, Path):
                path = item
            else:
                # Clean up path - remove any quotes or escape sequences
                clean_path = str(item).strip().strip('"\'')
                path = Path(clean_path)
            
            # Check if the path exists and what type it is
            if path.exists():
                if path.is_dir():
                    directories.append(path)
                elif path.is_file():
                    # Only add files that are supported image formats using magic bytes
                    if is_supported_image(path):
                        image_files.append(path)
            else:
                console.print(f"[bold yellow]Path not found:[/bold yellow] {item}")
        
        # Process directories if any were dropped
        dir_images = []
        for directory in directories:
            console.print(f"[bold]Scanning directory:[/bold] {directory}")
            dir_image_count = 0
            
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp', '.gif']:
                found_images = list(directory.glob(f'*{ext}')) + list(directory.glob(f'*{ext.upper()}'))
                dir_images.extend(found_images)
                dir_image_count += len(found_images)
            
            if dir_image_count > 0:
                console.print(f"[bold]Found {dir_image_count} images in:[/bold] {directory}")
            else:
                console.print(f"[bold yellow]No images found in:[/bold yellow] {directory}")
        
        # Combine images from files and directories
        all_images = image_files + dir_images
        
        if not all_images:
            console.print("[bold yellow]No supported images found.[/bold yellow]\n")
            if no_loop:
                break
            continue
        
        # Process all found images
        console.print(f"[bold]Processing {len(all_images)} images...[/bold]")
        # Pass base_dir_for_display here too for interactive mode
        processed = process_images(all_images, quality, no_overwrite, min_size_bytes, base_dir_for_display=Path.cwd())
        
        # Exit the loop if no_loop option is set
        if no_loop:
            break
            
        # Add a separator before next batch
        if processed:
            console.print("\n[dim]──────────────────────────────────────────[/dim]\n")


if __name__ == '__main__':
    # Check for MozJPEG installation
    try:
        subprocess.run(['cjpeg', '-version'], capture_output=True, check=False)
    except FileNotFoundError:
        console.print("[bold red]Error:[/bold red] MozJPEG (cjpeg) not found!")
        console.print("\nPlease install MozJPEG:")
        console.print("  • MacOS: [cyan]brew install mozjpeg[/cyan]")
        console.print("  • Ubuntu/Debian: [cyan]sudo apt install mozjpeg[/cyan]")
        console.print("  • Fedora: [cyan]sudo dnf install mozjpeg[/cyan]")
        console.print("  • NixOS: [cyan]nix-env -iA nixos.mozjpeg[/cyan] or add to configuration.nix")
        sys.exit(1)
    
    cli() 
